"""
API Manager (V2) - Clean Implementation

This module provides independent API key management for Stage 10 (Script Playground)
reconstruction. It implements automatic loading from config.json, environment variables,
and manual fallback mechanisms while maintaining complete independence from Stage 2.

Key Features:
- Automatic API key loading from multiple sources
- Robust error handling and fallback mechanisms
- Complete independence from Stage 2 configuration
- Secure API key validation and storage
- Integration with existing StateManager patterns

This is a clean reimplementation that works alongside the legacy implementation
without modifying existing API management systems.
"""

import logging
import os
import json
from typing import Optional, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum
import streamlit as st

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.api_manager_v2")


class APIKeySource(Enum):
    """Sources for API key loading."""
    CONFIG_FILE = "config_file"
    ENVIRONMENT = "environment"
    MANUAL_INPUT = "manual_input"
    SESSION_STATE = "session_state"
    NOT_FOUND = "not_found"


@dataclass
class APIKeyInfo:
    """Information about an API key and its source."""
    key: Optional[str]
    source: APIKeySource
    is_valid: bool
    error_message: Optional[str] = None
    config_path: Optional[str] = None
    env_var_name: Optional[str] = None


class APIManagerV2:
    """
    Independent API key manager for Stage 10 (V2).
    
    This class provides comprehensive API key management with automatic loading
    from multiple sources and robust error handling.
    """
    
    # Configuration file paths to check
    CONFIG_PATHS = [
        "config.json",
        "scriptweaver_config.json",
        "GretahAI_ScriptWeaver/config.json",
        "GretahAI_ScriptWeaver/scriptweaver_config.json"
    ]
    
    # Environment variable names to check
    ENV_VAR_NAMES = [
        "GOOGLE_AI_API_KEY",
        "GEMINI_API_KEY",
        "GOOGLE_API_KEY",
        "GRETAH_GOOGLE_AI_API_KEY"
    ]
    
    # Session state key for API key storage
    SESSION_KEY = "stage10_v2_api_key_info"
    
    def __init__(self):
        """Initialize the API manager."""
        self._cached_api_info: Optional[APIKeyInfo] = None
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5 minutes cache TTL
        
        logger.info("Initialized API Manager V2")
    
    def get_api_key(self, force_reload: bool = False) -> APIKeyInfo:
        """
        Get API key from the best available source.
        
        Args:
            force_reload: Force reload from sources instead of using cache
            
        Returns:
            APIKeyInfo: Information about the API key and its source
        """
        try:
            # Check cache first (unless force reload)
            if not force_reload and self._is_cache_valid():
                return self._cached_api_info
            
            # Try loading from session state first
            session_info = self._load_from_session_state()
            if session_info.is_valid:
                self._update_cache(session_info)
                return session_info
            
            # Try loading from config file
            config_info = self._load_from_config()
            if config_info.is_valid:
                self._save_to_session_state(config_info)
                self._update_cache(config_info)
                return config_info
            
            # Try loading from environment
            env_info = self._load_from_environment()
            if env_info.is_valid:
                self._save_to_session_state(env_info)
                self._update_cache(env_info)
                return env_info
            
            # No valid API key found
            no_key_info = APIKeyInfo(
                key=None,
                source=APIKeySource.NOT_FOUND,
                is_valid=False,
                error_message="No valid API key found in config, environment, or session state"
            )
            
            self._update_cache(no_key_info)
            return no_key_info
            
        except Exception as e:
            logger.error(f"Failed to get API key: {e}")
            error_info = APIKeyInfo(
                key=None,
                source=APIKeySource.NOT_FOUND,
                is_valid=False,
                error_message=f"Error loading API key: {str(e)}"
            )
            return error_info
    
    def set_manual_api_key(self, api_key: str) -> APIKeyInfo:
        """
        Set API key manually with validation.
        
        Args:
            api_key: Manual API key input
            
        Returns:
            APIKeyInfo: Information about the set API key
        """
        try:
            # Validate the API key
            is_valid = self._validate_api_key(api_key)
            
            api_info = APIKeyInfo(
                key=api_key if is_valid else None,
                source=APIKeySource.MANUAL_INPUT,
                is_valid=is_valid,
                error_message=None if is_valid else "Invalid API key format"
            )
            
            if is_valid:
                self._save_to_session_state(api_info)
                self._update_cache(api_info)
                logger.info("Manual API key set successfully")
            else:
                logger.warning("Invalid manual API key provided")
            
            return api_info
            
        except Exception as e:
            logger.error(f"Failed to set manual API key: {e}")
            return APIKeyInfo(
                key=None,
                source=APIKeySource.MANUAL_INPUT,
                is_valid=False,
                error_message=f"Error setting API key: {str(e)}"
            )
    
    def clear_api_key(self) -> None:
        """Clear stored API key from session state and cache."""
        try:
            if self.SESSION_KEY in st.session_state:
                del st.session_state[self.SESSION_KEY]
            
            self._cached_api_info = None
            self._cache_timestamp = 0
            
            logger.info("API key cleared from session state and cache")
            
        except Exception as e:
            logger.error(f"Failed to clear API key: {e}")
    
    def get_api_key_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of API key availability.
        
        Returns:
            Dict containing API key status information
        """
        try:
            api_info = self.get_api_key()
            
            status = {
                'has_api_key': api_info.is_valid,
                'source': api_info.source.value,
                'error_message': api_info.error_message,
                'sources_checked': {
                    'config_file': False,
                    'environment': False,
                    'session_state': False
                }
            }
            
            # Check each source individually for detailed status
            config_info = self._load_from_config()
            status['sources_checked']['config_file'] = config_info.is_valid
            if config_info.is_valid:
                status['config_path'] = config_info.config_path
            
            env_info = self._load_from_environment()
            status['sources_checked']['environment'] = env_info.is_valid
            if env_info.is_valid:
                status['env_var_name'] = env_info.env_var_name
            
            session_info = self._load_from_session_state()
            status['sources_checked']['session_state'] = session_info.is_valid
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get API key status: {e}")
            return {
                'has_api_key': False,
                'source': 'error',
                'error_message': str(e),
                'sources_checked': {}
            }
    
    def _load_from_config(self) -> APIKeyInfo:
        """Load API key from configuration files."""
        try:
            for config_path in self.CONFIG_PATHS:
                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)
                        
                        # Check various possible key names
                        api_key = (
                            config_data.get('google_ai_api_key') or
                            config_data.get('gemini_api_key') or
                            config_data.get('google_api_key') or
                            config_data.get('GOOGLE_AI_API_KEY')
                        )
                        
                        if api_key and self._validate_api_key(api_key):
                            logger.info(f"Loaded API key from config: {config_path}")
                            return APIKeyInfo(
                                key=api_key,
                                source=APIKeySource.CONFIG_FILE,
                                is_valid=True,
                                config_path=config_path
                            )
                    
                    except (json.JSONDecodeError, IOError) as e:
                        logger.warning(f"Failed to read config {config_path}: {e}")
                        continue
            
            return APIKeyInfo(
                key=None,
                source=APIKeySource.CONFIG_FILE,
                is_valid=False,
                error_message="No valid API key found in config files"
            )
            
        except Exception as e:
            logger.error(f"Error loading from config: {e}")
            return APIKeyInfo(
                key=None,
                source=APIKeySource.CONFIG_FILE,
                is_valid=False,
                error_message=str(e)
            )
    
    def _load_from_environment(self) -> APIKeyInfo:
        """Load API key from environment variables."""
        try:
            for env_var in self.ENV_VAR_NAMES:
                api_key = os.getenv(env_var)
                if api_key and self._validate_api_key(api_key):
                    logger.info(f"Loaded API key from environment: {env_var}")
                    return APIKeyInfo(
                        key=api_key,
                        source=APIKeySource.ENVIRONMENT,
                        is_valid=True,
                        env_var_name=env_var
                    )
            
            return APIKeyInfo(
                key=None,
                source=APIKeySource.ENVIRONMENT,
                is_valid=False,
                error_message="No valid API key found in environment variables"
            )
            
        except Exception as e:
            logger.error(f"Error loading from environment: {e}")
            return APIKeyInfo(
                key=None,
                source=APIKeySource.ENVIRONMENT,
                is_valid=False,
                error_message=str(e)
            )
    
    def _load_from_session_state(self) -> APIKeyInfo:
        """Load API key from session state."""
        try:
            if self.SESSION_KEY in st.session_state:
                stored_info = st.session_state[self.SESSION_KEY]
                if isinstance(stored_info, APIKeyInfo) and stored_info.is_valid:
                    return stored_info
            
            return APIKeyInfo(
                key=None,
                source=APIKeySource.SESSION_STATE,
                is_valid=False,
                error_message="No valid API key in session state"
            )
            
        except Exception as e:
            logger.error(f"Error loading from session state: {e}")
            return APIKeyInfo(
                key=None,
                source=APIKeySource.SESSION_STATE,
                is_valid=False,
                error_message=str(e)
            )
    
    def _save_to_session_state(self, api_info: APIKeyInfo) -> None:
        """Save API key info to session state."""
        try:
            if api_info.is_valid:
                st.session_state[self.SESSION_KEY] = api_info
                logger.debug("Saved API key info to session state")
        except Exception as e:
            logger.error(f"Failed to save to session state: {e}")
    
    def _validate_api_key(self, api_key: str) -> bool:
        """
        Validate API key format.
        
        Args:
            api_key: API key to validate
            
        Returns:
            bool: True if API key format is valid
        """
        if not api_key or not isinstance(api_key, str):
            return False
        
        # Basic validation - Google AI API keys typically start with specific patterns
        api_key = api_key.strip()
        
        # Check minimum length
        if len(api_key) < 20:
            return False
        
        # Check for common Google AI API key patterns
        if api_key.startswith(('AIza', 'AIZA')):
            return True
        
        # Allow other patterns but with stricter length requirements
        if len(api_key) >= 30:
            return True
        
        return False
    
    def _is_cache_valid(self) -> bool:
        """Check if cached API info is still valid."""
        import time
        return (
            self._cached_api_info is not None and
            time.time() - self._cache_timestamp < self._cache_ttl
        )
    
    def _update_cache(self, api_info: APIKeyInfo) -> None:
        """Update the API info cache."""
        import time
        self._cached_api_info = api_info
        self._cache_timestamp = time.time()
