"""
Stage 10 Integration (V2) - Safe Migration Helper

This module provides safe integration between the legacy Stage 10 implementation
and the new V2 implementation. It allows for controlled testing and migration
without disrupting existing functionality.

Key Features:
- Feature flag-based switching between legacy and V2 implementations
- Safe fallback mechanisms
- Migration utilities
- Compatibility checks
- Performance monitoring

This module ensures that the V2 implementation can be safely tested and deployed
alongside the existing legacy implementation.
"""

import logging
import os
from typing import Dict, Any, Optional
from enum import Enum
import streamlit as st

# Import both implementations
from stages.stage10 import stage10_script_playground as stage10_legacy
from stages.stage10_v2 import stage10_script_playground_v2

# Import state managers
from state_manager import StateManager
from core.stage10_state_v2 import Stage10StateManager

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.stage10_integration_v2")


class Stage10Implementation(Enum):
    """Available Stage 10 implementations."""
    LEGACY = "legacy"
    V2 = "v2"
    AUTO = "auto"


class Stage10IntegrationV2:
    """
    Integration manager for Stage 10 V2 implementation.
    
    This class provides safe switching between legacy and V2 implementations
    with proper fallback mechanisms and compatibility checks.
    """
    
    # Environment variable for implementation selection
    IMPLEMENTATION_ENV_VAR = "STAGE10_IMPLEMENTATION"
    
    # Session state key for user preference
    USER_PREFERENCE_KEY = "stage10_implementation_preference"
    
    # Feature flags
    V2_ENABLED_ENV_VAR = "STAGE10_V2_ENABLED"
    V2_BETA_TESTING_ENV_VAR = "STAGE10_V2_BETA_TESTING"
    
    def __init__(self):
        """Initialize the integration manager."""
        self._performance_metrics = {}
        logger.info("Initialized Stage 10 Integration V2")
    
    def get_active_implementation(self) -> Stage10Implementation:
        """
        Determine which implementation should be used.
        
        Returns:
            Stage10Implementation: The implementation to use
        """
        try:
            # Check environment variable first
            env_impl = os.getenv(self.IMPLEMENTATION_ENV_VAR, "").lower()
            if env_impl in ["legacy", "v2"]:
                return Stage10Implementation(env_impl)
            
            # Check if V2 is explicitly enabled
            v2_enabled = os.getenv(self.V2_ENABLED_ENV_VAR, "false").lower() == "true"
            if v2_enabled:
                return Stage10Implementation.V2
            
            # Check beta testing flag
            beta_testing = os.getenv(self.V2_BETA_TESTING_ENV_VAR, "false").lower() == "true"
            if beta_testing:
                # Check user preference in session state
                user_pref = st.session_state.get(self.USER_PREFERENCE_KEY)
                if user_pref in ["legacy", "v2"]:
                    return Stage10Implementation(user_pref)
                return Stage10Implementation.V2  # Default to V2 for beta testing
            
            # Default to legacy for safety
            return Stage10Implementation.LEGACY
            
        except Exception as e:
            logger.error(f"Error determining implementation: {e}")
            return Stage10Implementation.LEGACY
    
    def render_stage10(self, state: StateManager) -> None:
        """
        Render Stage 10 using the appropriate implementation.
        
        Args:
            state: StateManager instance
        """
        try:
            implementation = self.get_active_implementation()
            
            # Add implementation selector for beta testing
            if os.getenv(self.V2_BETA_TESTING_ENV_VAR, "false").lower() == "true":
                self._render_implementation_selector()
            
            # Track performance
            import time
            start_time = time.time()
            
            # Render appropriate implementation
            if implementation == Stage10Implementation.V2:
                self._render_v2_with_fallback(state)
            else:
                self._render_legacy_with_monitoring(state)
            
            # Record performance metrics
            duration = time.time() - start_time
            self._record_performance_metric(implementation.value, duration)
            
        except Exception as e:
            logger.error(f"Error rendering Stage 10: {e}")
            # Fallback to legacy on any error
            self._render_legacy_with_monitoring(state)
    
    def _render_implementation_selector(self) -> None:
        """Render implementation selector for beta testing."""
        with st.sidebar:
            st.markdown("### 🧪 Beta Testing")
            
            current_impl = st.session_state.get(self.USER_PREFERENCE_KEY, "v2")
            
            new_impl = st.radio(
                "Choose Implementation:",
                options=["v2", "legacy"],
                index=0 if current_impl == "v2" else 1,
                format_func=lambda x: "🆕 V2 (New)" if x == "v2" else "🏛️ Legacy (Current)",
                key="stage10_impl_selector"
            )
            
            if new_impl != current_impl:
                st.session_state[self.USER_PREFERENCE_KEY] = new_impl
                st.rerun()
            
            # Show current implementation info
            if new_impl == "v2":
                st.info("🆕 Using Stage 10 V2 (Clean Implementation)")
            else:
                st.info("🏛️ Using Stage 10 Legacy (Current)")
    
    def _render_v2_with_fallback(self, state: StateManager) -> None:
        """
        Render V2 implementation with fallback to legacy on error.
        
        Args:
            state: StateManager instance
        """
        try:
            # Check V2 compatibility
            if not self._check_v2_compatibility():
                logger.warning("V2 compatibility check failed, falling back to legacy")
                self._render_legacy_with_monitoring(state)
                return
            
            # Render V2 implementation
            stage10_script_playground_v2(state)
            
            # Show V2 indicator
            with st.sidebar:
                st.success("🆕 Running Stage 10 V2")
            
        except Exception as e:
            logger.error(f"V2 implementation failed: {e}")
            st.error(f"❌ V2 implementation error: {str(e)}")
            
            # Fallback to legacy
            st.warning("⚠️ Falling back to legacy implementation...")
            self._render_legacy_with_monitoring(state)
    
    def _render_legacy_with_monitoring(self, state: StateManager) -> None:
        """
        Render legacy implementation with monitoring.
        
        Args:
            state: StateManager instance
        """
        try:
            # Render legacy implementation
            stage10_legacy(state)
            
            # Show legacy indicator
            with st.sidebar:
                st.info("🏛️ Running Stage 10 Legacy")
            
        except Exception as e:
            logger.error(f"Legacy implementation failed: {e}")
            st.error(f"❌ Stage 10 error: {str(e)}")
    
    def _check_v2_compatibility(self) -> bool:
        """
        Check if V2 implementation is compatible with current environment.
        
        Returns:
            bool: True if V2 is compatible
        """
        try:
            # Check required modules
            required_modules = [
                'core.stage10_state_v2',
                'core.template_storage_v2',
                'core.api_manager_v2'
            ]
            
            for module_name in required_modules:
                try:
                    __import__(module_name)
                except ImportError as e:
                    logger.error(f"Required module not available: {module_name} - {e}")
                    return False
            
            # Check Streamlit version compatibility
            import streamlit as st
            if not hasattr(st, 'rerun'):
                logger.error("Streamlit version too old for V2 implementation")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Compatibility check failed: {e}")
            return False
    
    def _record_performance_metric(self, implementation: str, duration: float) -> None:
        """
        Record performance metrics for comparison.
        
        Args:
            implementation: Implementation name
            duration: Execution duration in seconds
        """
        try:
            if implementation not in self._performance_metrics:
                self._performance_metrics[implementation] = []
            
            self._performance_metrics[implementation].append(duration)
            
            # Keep only last 10 measurements
            if len(self._performance_metrics[implementation]) > 10:
                self._performance_metrics[implementation] = self._performance_metrics[implementation][-10:]
            
            logger.debug(f"Performance: {implementation} took {duration:.3f}s")
            
        except Exception as e:
            logger.error(f"Failed to record performance metric: {e}")
    
    def get_performance_comparison(self) -> Dict[str, Any]:
        """
        Get performance comparison between implementations.
        
        Returns:
            Dict containing performance comparison data
        """
        try:
            comparison = {}
            
            for impl, durations in self._performance_metrics.items():
                if durations:
                    comparison[impl] = {
                        'average_duration': sum(durations) / len(durations),
                        'min_duration': min(durations),
                        'max_duration': max(durations),
                        'sample_count': len(durations)
                    }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to get performance comparison: {e}")
            return {}
    
    def migrate_legacy_state_to_v2(self) -> bool:
        """
        Migrate legacy Stage 10 state to V2 format.
        
        Returns:
            bool: True if migration was successful
        """
        try:
            logger.info("Starting legacy state migration to V2")
            
            # Get legacy state
            main_state = StateManager.get(st)
            
            # Check if there's any legacy Stage 10 state to migrate
            legacy_keys = [k for k in st.session_state.keys() if k.startswith('stage10_') and not k.startswith('stage10_v2_')]
            
            if not legacy_keys:
                logger.info("No legacy state to migrate")
                return True
            
            # Migrate relevant state
            v2_state = Stage10StateManager.get_state()
            
            # Migrate API key if available
            legacy_api_key = getattr(main_state, 'stage10_google_api_key', None)
            if legacy_api_key:
                Stage10StateManager.set_api_key(legacy_api_key, "migrated_from_legacy")
                logger.info("Migrated API key from legacy state")
            
            # Clean up legacy keys (optional)
            cleanup_legacy = os.getenv("STAGE10_V2_CLEANUP_LEGACY", "false").lower() == "true"
            if cleanup_legacy:
                for key in legacy_keys:
                    if key in st.session_state:
                        del st.session_state[key]
                logger.info(f"Cleaned up {len(legacy_keys)} legacy state keys")
            
            logger.info("Legacy state migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Legacy state migration failed: {e}")
            return False


# Global integration manager instance
integration_manager = Stage10IntegrationV2()


def render_stage10_integrated(state: StateManager) -> None:
    """
    Main entry point for integrated Stage 10 rendering.
    
    This function automatically selects the appropriate implementation
    based on configuration and feature flags.
    
    Args:
        state: StateManager instance
    """
    integration_manager.render_stage10(state)
