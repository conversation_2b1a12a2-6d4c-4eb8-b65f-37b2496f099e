"""
Stage 10 State Management (V2) - Clean Implementation

This module provides centralized state management specifically for Stage 10 (Script Playground)
reconstruction. It implements clean state patterns, proper cleanup mechanisms, and integration
with the existing StateManager while maintaining complete independence.

Key Features:
- Centralized Stage 10 state handling with proper cleanup
- Template selection and metadata management
- Gap analysis state tracking
- API key management state
- Session state cleanup patterns
- Integration with existing StateManager patterns

This is a clean reimplementation that works alongside the legacy stage10.py
without modifying existing functionality.
"""

import logging
import time
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Set
from enum import Enum
import streamlit as st

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.stage10_state_v2")

class Stage10WorkflowState(Enum):
    """Stage 10 workflow states for clean state management."""
    TEMPLATE_SELECTION = "template_selection"
    GAP_ANALYSIS = "gap_analysis"
    SCRIPT_GENERATION = "script_generation"
    SCRIPT_PREVIEW = "script_preview"
    COMPLETED = "completed"


@dataclass
class Stage10SessionState:
    """
    Centralized state container for Stage 10 (V2) operations.
    
    This class manages all Stage 10-specific state in a clean, organized manner
    while integrating with the existing StateManager patterns.
    """
    
    # ───── Core Workflow State ─────
    workflow_state: Stage10WorkflowState = Stage10WorkflowState.TEMPLATE_SELECTION
    session_id: str = ""
    last_activity: float = field(default_factory=time.time)
    
    # ───── Template Management ─────
    selected_template_id: Optional[str] = None
    selected_template_metadata: Dict[str, Any] = field(default_factory=dict)
    available_templates: List[Dict[str, Any]] = field(default_factory=list)
    template_loading_cache: Dict[str, Any] = field(default_factory=dict)
    
    # ───── Target Test Case ─────
    selected_test_case_id: Optional[str] = None
    selected_test_case_data: Dict[str, Any] = field(default_factory=dict)
    
    # ───── Gap Analysis State ─────
    gap_analysis_result: Optional[Dict[str, Any]] = None
    gap_analysis_cache_key: Optional[str] = None
    user_provided_gap_data: Dict[str, Any] = field(default_factory=dict)
    gap_analysis_completed: bool = False
    gap_analysis_skipped: bool = False
    
    # ───── API Management ─────
    google_api_key: Optional[str] = None
    api_key_source: Optional[str] = None  # "config", "environment", "manual"
    api_key_validated: bool = False
    
    # ───── Script Generation ─────
    generated_script_content: Optional[str] = None
    generated_script_metadata: Dict[str, Any] = field(default_factory=dict)
    generation_timestamp: Optional[float] = None
    generation_request_id: Optional[str] = None
    
    # ───── UI State ─────
    show_template_preview: bool = False
    show_gap_analysis_details: bool = False
    show_generation_progress: bool = False
    current_error_message: Optional[str] = None
    
    # ───── Cleanup Tracking ─────
    temporary_session_keys: Set[str] = field(default_factory=set)
    cleanup_required: bool = False


class Stage10StateManager:
    """
    Clean state manager for Stage 10 (V2) operations.
    
    This class provides centralized state management with proper cleanup,
    caching, and integration with the existing StateManager patterns.
    """
    
    # Session state key for Stage 10 V2 state
    SESSION_STATE_KEY = "stage10_v2_state"
    
    # Keys that should be cleaned up between sessions
    CLEANUP_PATTERNS = [
        "stage10_v2_",
        "gap_analysis_v2_",
        "template_selection_v2_",
        "script_generation_v2_",
        "api_key_v2_"
    ]
    
    @classmethod
    def get_state(cls) -> Stage10SessionState:
        """
        Get or initialize the Stage 10 V2 session state.
        
        Returns:
            Stage10SessionState: Current session state
        """
        if cls.SESSION_STATE_KEY not in st.session_state:
            # Initialize new state
            state = Stage10SessionState()
            state.session_id = cls._generate_session_id()
            st.session_state[cls.SESSION_STATE_KEY] = state
            logger.info(f"Initialized new Stage 10 V2 state with session ID: {state.session_id}")
        
        state = st.session_state[cls.SESSION_STATE_KEY]
        state.last_activity = time.time()
        return state
    
    @classmethod
    def update_workflow_state(cls, new_state: Stage10WorkflowState, reason: str = "") -> None:
        """
        Update the workflow state with proper logging and cleanup.
        
        Args:
            new_state: New workflow state
            reason: Reason for the state change
        """
        state = cls.get_state()
        old_state = state.workflow_state
        
        if old_state != new_state:
            state.workflow_state = new_state
            logger.info(f"Stage 10 V2 workflow state changed: {old_state.value} -> {new_state.value} ({reason})")
            
            # Trigger cleanup if needed
            cls._cleanup_state_transition(old_state, new_state)
            
            # Update session state
            st.session_state[cls.SESSION_STATE_KEY] = state
    
    @classmethod
    def set_selected_template(cls, template_id: str, template_metadata: Dict[str, Any]) -> None:
        """
        Set the selected template with metadata.
        
        Args:
            template_id: Template identifier
            template_metadata: Template metadata
        """
        state = cls.get_state()
        state.selected_template_id = template_id
        state.selected_template_metadata = template_metadata.copy()
        
        # Clear dependent state
        state.gap_analysis_result = None
        state.gap_analysis_completed = False
        state.gap_analysis_skipped = False
        state.user_provided_gap_data.clear()
        state.generated_script_content = None
        
        st.session_state[cls.SESSION_STATE_KEY] = state
        logger.info(f"Selected template: {template_id}")
    
    @classmethod
    def set_selected_test_case(cls, test_case_id: str, test_case_data: Dict[str, Any]) -> None:
        """
        Set the selected target test case.
        
        Args:
            test_case_id: Test case identifier
            test_case_data: Test case data
        """
        state = cls.get_state()
        state.selected_test_case_id = test_case_id
        state.selected_test_case_data = test_case_data.copy()
        
        # Clear dependent state
        state.gap_analysis_result = None
        state.gap_analysis_completed = False
        state.gap_analysis_skipped = False
        state.user_provided_gap_data.clear()
        state.generated_script_content = None
        
        st.session_state[cls.SESSION_STATE_KEY] = state
        logger.info(f"Selected test case: {test_case_id}")
    
    @classmethod
    def set_api_key(cls, api_key: str, source: str) -> None:
        """
        Set the Google AI API key with source tracking.
        
        Args:
            api_key: API key value
            source: Source of the API key ("config", "environment", "manual")
        """
        state = cls.get_state()
        state.google_api_key = api_key
        state.api_key_source = source
        state.api_key_validated = bool(api_key)
        
        st.session_state[cls.SESSION_STATE_KEY] = state
        logger.info(f"API key set from source: {source}")
    
    @classmethod
    def clear_state(cls, preserve_api_key: bool = True) -> None:
        """
        Clear Stage 10 V2 state with optional API key preservation.
        
        Args:
            preserve_api_key: Whether to preserve API key settings
        """
        state = cls.get_state()
        
        # Preserve API key if requested
        api_key = state.google_api_key if preserve_api_key else None
        api_key_source = state.api_key_source if preserve_api_key else None
        api_key_validated = state.api_key_validated if preserve_api_key else False
        
        # Create new clean state
        new_state = Stage10SessionState()
        new_state.session_id = cls._generate_session_id()
        
        if preserve_api_key:
            new_state.google_api_key = api_key
            new_state.api_key_source = api_key_source
            new_state.api_key_validated = api_key_validated
        
        st.session_state[cls.SESSION_STATE_KEY] = new_state
        logger.info(f"Cleared Stage 10 V2 state (API key preserved: {preserve_api_key})")
    
    @classmethod
    def cleanup_session_state(cls) -> None:
        """
        Clean up temporary session state keys created by Stage 10 V2.
        """
        keys_to_remove = []
        
        for key in st.session_state.keys():
            for pattern in cls.CLEANUP_PATTERNS:
                if key.startswith(pattern):
                    keys_to_remove.append(key)
                    break
        
        for key in keys_to_remove:
            del st.session_state[key]
        
        if keys_to_remove:
            logger.info(f"Cleaned up {len(keys_to_remove)} temporary session state keys")
    
    @classmethod
    def _generate_session_id(cls) -> str:
        """Generate a unique session ID for tracking."""
        import uuid
        return f"stage10_v2_{int(time.time())}_{str(uuid.uuid4())[:8]}"
    
    @classmethod
    def _cleanup_state_transition(cls, old_state: Stage10WorkflowState, new_state: Stage10WorkflowState) -> None:
        """
        Perform cleanup when transitioning between workflow states.
        
        Args:
            old_state: Previous workflow state
            new_state: New workflow state
        """
        state = cls.get_state()
        
        # Clear UI state when moving between major workflow steps
        if old_state != new_state:
            state.current_error_message = None
            state.show_generation_progress = False
            
            # Specific cleanup based on transitions
            if new_state == Stage10WorkflowState.TEMPLATE_SELECTION:
                # Reset everything when going back to template selection
                state.selected_template_id = None
                state.selected_template_metadata.clear()
                state.gap_analysis_result = None
                state.gap_analysis_completed = False
                state.user_provided_gap_data.clear()
                state.generated_script_content = None
            
            elif new_state == Stage10WorkflowState.GAP_ANALYSIS:
                # Clear generation state when going to gap analysis
                state.generated_script_content = None
                state.generation_timestamp = None
            
            st.session_state[cls.SESSION_STATE_KEY] = state
