"""
Template Storage Interface (V2) - Clean Implementation

This module provides a clean interface for loading and managing script templates
for Stage 10 (Script Playground) reconstruction. It integrates with the existing
script_storage.db while providing modern caching and metadata management.

Key Features:
- Clean template loading from existing script_storage.db
- Template metadata preservation and enhancement
- Caching for performance optimization
- Template filtering and search capabilities
- Integration with existing ScriptStorage patterns

This is a clean reimplementation that works alongside the legacy implementation
without modifying existing storage mechanisms.
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import streamlit as st

# Import existing script storage
from core.script_storage import ScriptStorage

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.template_storage_v2")


@dataclass
class TemplateMetadata:
    """Enhanced metadata for script templates."""
    
    # Core identification
    template_id: str
    original_script_id: str
    template_name: str
    
    # Source information
    source_test_case_id: str
    source_test_case_objective: str
    creation_timestamp: float
    
    # Template characteristics
    script_type: str  # 'optimized', 'combined', 'step'
    line_count: int
    file_size: int
    
    # Validation status
    locators_validated: bool
    optimization_level: str  # 'basic', 'optimized', 'enhanced'
    quality_score: Optional[float] = None
    
    # Usage tracking
    usage_count: int = 0
    last_used: Optional[float] = None
    success_rate: Optional[float] = None
    
    # Template content analysis
    detected_elements: List[str] = None
    automation_patterns: List[str] = None
    error_handling_level: str = "basic"
    
    def __post_init__(self):
        if self.detected_elements is None:
            self.detected_elements = []
        if self.automation_patterns is None:
            self.automation_patterns = []


class TemplateStorageV2:
    """
    Clean template storage interface for Stage 10 (V2).
    
    This class provides a modern interface for loading and managing script templates
    while integrating with the existing script_storage.db infrastructure.
    """
    
    def __init__(self):
        """Initialize the template storage interface."""
        self.script_storage = ScriptStorage()
        self._template_cache = {}
        self._metadata_cache = {}
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5 minutes cache TTL
        
        logger.info("Initialized Template Storage V2")
    
    @st.cache_data(ttl=300)
    def load_available_templates(_self) -> List[TemplateMetadata]:
        """
        Load all available templates with enhanced metadata.
        
        Returns:
            List[TemplateMetadata]: Available templates with metadata
        """
        try:
            logger.info("Loading available templates from storage")
            
            # Get all optimized scripts from storage
            all_scripts = _self.script_storage.get_all_scripts()
            templates = []
            
            for script in all_scripts:
                # Filter for optimized scripts that can serve as templates
                if script.get('script_type') == 'optimized':
                    template_metadata = _self._create_template_metadata(script)
                    if template_metadata:
                        templates.append(template_metadata)
            
            # Sort by quality score and recency
            templates.sort(key=lambda t: (
                t.quality_score or 0,
                t.creation_timestamp
            ), reverse=True)
            
            logger.info(f"Loaded {len(templates)} templates")
            return templates
            
        except Exception as e:
            logger.error(f"Failed to load templates: {e}")
            return []
    
    def get_template_content(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get full template content by ID.
        
        Args:
            template_id: Template identifier
            
        Returns:
            Dict containing template content and metadata, or None if not found
        """
        try:
            # Check cache first
            if template_id in self._template_cache:
                cache_entry = self._template_cache[template_id]
                if time.time() - cache_entry['timestamp'] < self._cache_ttl:
                    return cache_entry['content']
            
            # Load from storage
            script = self.script_storage.get_script_by_id(template_id)
            if not script:
                logger.warning(f"Template not found: {template_id}")
                return None
            
            # Enhance with template-specific metadata
            template_content = {
                'id': template_id,
                'content': script.get('content', ''),
                'metadata': self._enhance_script_metadata(script),
                'original_script': script
            }
            
            # Cache the result
            self._template_cache[template_id] = {
                'content': template_content,
                'timestamp': time.time()
            }
            
            logger.info(f"Loaded template content: {template_id}")
            return template_content
            
        except Exception as e:
            logger.error(f"Failed to load template content {template_id}: {e}")
            return None
    
    def search_templates(self, query: str, filters: Dict[str, Any] = None) -> List[TemplateMetadata]:
        """
        Search templates by query and filters.
        
        Args:
            query: Search query string
            filters: Optional filters (test_case_id, script_type, etc.)
            
        Returns:
            List[TemplateMetadata]: Matching templates
        """
        try:
            all_templates = self.load_available_templates()
            
            if not query and not filters:
                return all_templates
            
            matching_templates = []
            query_lower = query.lower() if query else ""
            
            for template in all_templates:
                # Text search
                if query:
                    searchable_text = f"{template.template_name} {template.source_test_case_id} {template.source_test_case_objective}".lower()
                    if query_lower not in searchable_text:
                        continue
                
                # Apply filters
                if filters:
                    if 'script_type' in filters and template.script_type != filters['script_type']:
                        continue
                    if 'min_quality' in filters and (template.quality_score or 0) < filters['min_quality']:
                        continue
                    if 'test_case_id' in filters and template.source_test_case_id != filters['test_case_id']:
                        continue
                
                matching_templates.append(template)
            
            logger.info(f"Found {len(matching_templates)} templates matching query: '{query}'")
            return matching_templates
            
        except Exception as e:
            logger.error(f"Template search failed: {e}")
            return []
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about available templates.
        
        Returns:
            Dict containing template statistics
        """
        try:
            templates = self.load_available_templates()
            
            stats = {
                'total_templates': len(templates),
                'by_type': {},
                'by_optimization_level': {},
                'average_quality': 0,
                'recent_templates': 0  # Created in last 7 days
            }
            
            if not templates:
                return stats
            
            # Calculate statistics
            quality_scores = []
            recent_threshold = time.time() - (7 * 24 * 60 * 60)  # 7 days ago
            
            for template in templates:
                # Count by type
                script_type = template.script_type
                stats['by_type'][script_type] = stats['by_type'].get(script_type, 0) + 1
                
                # Count by optimization level
                opt_level = template.optimization_level
                stats['by_optimization_level'][opt_level] = stats['by_optimization_level'].get(opt_level, 0) + 1
                
                # Quality scores
                if template.quality_score is not None:
                    quality_scores.append(template.quality_score)
                
                # Recent templates
                if template.creation_timestamp > recent_threshold:
                    stats['recent_templates'] += 1
            
            # Average quality
            if quality_scores:
                stats['average_quality'] = sum(quality_scores) / len(quality_scores)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get template statistics: {e}")
            return {'total_templates': 0}
    
    def _create_template_metadata(self, script: Dict[str, Any]) -> Optional[TemplateMetadata]:
        """
        Create enhanced template metadata from script data.
        
        Args:
            script: Script data from storage
            
        Returns:
            TemplateMetadata or None if script is not suitable as template
        """
        try:
            script_id = script.get('id')
            if not script_id:
                return None
            
            # Extract metadata
            metadata = script.get('metadata', {})
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except json.JSONDecodeError:
                    metadata = {}
            
            # Create template metadata
            template_metadata = TemplateMetadata(
                template_id=script_id,
                original_script_id=script_id,
                template_name=self._generate_template_name(script),
                source_test_case_id=script.get('test_case_id', 'Unknown'),
                source_test_case_objective=metadata.get('test_case_objective', 'No objective specified'),
                creation_timestamp=self._parse_timestamp(script.get('created_at')),
                script_type=script.get('script_type', 'unknown'),
                line_count=metadata.get('line_count', 0),
                file_size=metadata.get('file_size', 0),
                locators_validated=True,  # Optimized scripts have validated locators
                optimization_level=self._determine_optimization_level(script, metadata),
                quality_score=self._calculate_quality_score(script, metadata)
            )
            
            return template_metadata
            
        except Exception as e:
            logger.error(f"Failed to create template metadata for script {script.get('id')}: {e}")
            return None
    
    def _enhance_script_metadata(self, script: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance script metadata for template usage.
        
        Args:
            script: Original script data
            
        Returns:
            Enhanced metadata dictionary
        """
        try:
            original_metadata = script.get('metadata', {})
            if isinstance(original_metadata, str):
                try:
                    original_metadata = json.loads(original_metadata)
                except json.JSONDecodeError:
                    original_metadata = {}
            
            enhanced_metadata = original_metadata.copy()
            
            # Add template-specific enhancements
            enhanced_metadata.update({
                'template_ready': True,
                'locators_validated': True,
                'optimization_completed': True,
                'template_created_at': time.time(),
                'template_version': '2.0'
            })
            
            return enhanced_metadata
            
        except Exception as e:
            logger.error(f"Failed to enhance metadata: {e}")
            return {}
    
    def _generate_template_name(self, script: Dict[str, Any]) -> str:
        """Generate a user-friendly template name."""
        test_case_id = script.get('test_case_id', 'Unknown')
        script_type = script.get('script_type', 'script')
        return f"{test_case_id} ({script_type.title()})"
    
    def _parse_timestamp(self, timestamp_value) -> float:
        """Parse timestamp from various formats."""
        if isinstance(timestamp_value, (int, float)):
            return float(timestamp_value)
        elif isinstance(timestamp_value, str):
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(timestamp_value.replace('Z', '+00:00'))
                return dt.timestamp()
            except:
                return time.time()
        else:
            return time.time()
    
    def _determine_optimization_level(self, script: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Determine the optimization level of the script."""
        script_type = script.get('script_type', '')
        
        if script_type == 'optimized':
            return 'optimized'
        elif script_type == 'combined':
            return 'enhanced'
        else:
            return 'basic'
    
    def _calculate_quality_score(self, script: Dict[str, Any], metadata: Dict[str, Any]) -> float:
        """Calculate a quality score for the template."""
        score = 0.5  # Base score
        
        # Script type bonus
        if script.get('script_type') == 'optimized':
            score += 0.3
        elif script.get('script_type') == 'combined':
            score += 0.2
        
        # Size and complexity indicators
        line_count = metadata.get('line_count', 0)
        if line_count > 50:
            score += 0.1
        if line_count > 100:
            score += 0.1
        
        # Validation status
        validation_status = metadata.get('validation_status', '')
        if validation_status == 'passed':
            score += 0.2
        
        return min(1.0, score)  # Cap at 1.0
