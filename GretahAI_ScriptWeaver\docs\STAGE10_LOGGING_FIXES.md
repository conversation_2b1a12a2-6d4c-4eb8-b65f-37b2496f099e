# Stage 10 Logging Configuration Fixes

## Overview

This document describes the comprehensive fixes applied to resolve incomplete logging visibility in GretahAI Stage 10 (Script Template Manager). The fixes ensure all debug logs are properly captured and visible in debug output files.

## Problems Identified

### 1. Dual Logging Systems
Stage 10 was using two separate, unintegrated logging systems:
- **Standard Python logger** (`logger`) - used for `logger.debug()` calls throughout stage10.py
- **Enhanced Stage 10 logger** (`get_stage10_logger()`) - used for specialized logging functions

### 2. Log Level Filtering
The standard Python logger was configured at INFO level in `debug_utils.py`, causing all `logger.debug()` calls to be filtered out and not appear in any output.

### 3. Missing File Output
The standard logger had no file handler, so debug messages were only going to console and being lost.

### 4. Incomplete Coverage
Many Stage 10 operations used `logger.debug()` which wasn't being captured in the comprehensive logging system.

## Solutions Implemented

### 1. Dynamic Log Level Configuration

**File: `debug_utils.py`**
- Modified logging configuration to respect `SCRIPTWEAVER_DEBUG` environment variable
- Set log level to DEBUG when debug mode is enabled, INFO otherwise

```python
# Before
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# After
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")
log_level = logging.DEBUG if DEBUG_MODE else logging.INFO
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### 2. Enhanced Stage 10 Logger Configuration

**File: `stages/stage10.py`**
- Added debug mode awareness to standard logger
- Added file handler for standard logger when debug mode is enabled
- Created session-specific log files for standard logger

```python
# Set logger level based on debug mode
if DEBUG_MODE:
    logger.setLevel(logging.DEBUG)
    
    # Add file handler for standard logger to capture debug messages
    debug_logs_dir = Path("debug_logs/stage10")
    debug_logs_dir.mkdir(parents=True, exist_ok=True)
    
    session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    standard_log_file = debug_logs_dir / f"stage10_standard_{session_id}.log"
    
    if not any(isinstance(handler, logging.FileHandler) for handler in logger.handlers):
        file_handler = logging.FileHandler(standard_log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
```

### 3. Integrated Logging Functions

**File: `stages/stage10.py`**
- Created integrated logging functions that log to both systems simultaneously
- Ensures all debug messages are captured in both standard and enhanced loggers

```python
def _integrated_debug_log(message: str, context: str = "stage10"):
    """Log to both standard logger and enhanced Stage 10 logger."""
    # Log to standard logger (will go to file if DEBUG_MODE is enabled)
    logger.debug(f"[{context}] {message}")
    
    # Also log to enhanced Stage 10 logger if available
    try:
        enhanced_logger = get_stage10_logger()
        enhanced_logger.debug(message, context=context)
    except Exception as e:
        logger.warning(f"Enhanced logging failed: {e}")
```

### 4. Updated Critical Logging Calls

**File: `stages/stage10.py`**
- Updated key `logger.debug()` calls to use integrated logging functions
- Focused on critical areas: render function, gap analysis, script generation

```python
# Before
logger.debug(f"[stage10.render] Stage 10 render started - session state keys: {len(st.session_state.keys())}")

# After
_integrated_debug_log(f"Stage 10 render started - session state keys: {len(st.session_state.keys())}", "stage10.render")
```

## Results

### Log File Structure
When `SCRIPTWEAVER_DEBUG=true` is set, Stage 10 now creates two types of log files:

1. **Enhanced Logger Files**: `debug_logs/stage10/stage10_session_YYYYMMDD_HHMMSS.log`
   - Comprehensive logging with structured contexts
   - UI integration for real-time viewing
   - Specialized logging for gap analysis, script generation, etc.

2. **Standard Logger Files**: `debug_logs/stage10/stage10_standard_YYYYMMDD_HHMMSS.log`
   - All standard `logger.debug()` calls
   - Integration with enhanced logger for complete coverage

### Comprehensive Coverage
All Stage 10 operations now have proper logging:
- ✅ Interactive element selector workflow
- ✅ Gap analysis processes  
- ✅ Script generation workflows
- ✅ Session state management
- ✅ UI interactions
- ✅ Error handling and debugging

### Debug Panel Integration
The enhanced Stage 10 logger's debug panel is already integrated into the main render function, providing real-time log viewing in the Streamlit UI when debug mode is enabled.

## Testing

A comprehensive test suite was created and executed to verify all logging functionality:

```
✅ Logging Setup: PASS
✅ Enhanced Stage 10 Logger: PASS  
✅ Integrated Logging: PASS
✅ Log Files Check: PASS

Overall: ✅ ALL TESTS PASSED
```

## Usage

### Enable Debug Logging
Set the environment variable:
```bash
export SCRIPTWEAVER_DEBUG=true
```

Or in Python:
```python
import os
os.environ["SCRIPTWEAVER_DEBUG"] = "true"
```

### View Logs
1. **Real-time UI**: Debug panel appears in Stage 10 when debug mode is enabled
2. **Log Files**: Check `debug_logs/stage10/` directory for session-specific log files
3. **Console Output**: Debug messages also appear in console when debug mode is enabled

## Impact

- **Complete Visibility**: All Stage 10 debug information is now captured and accessible
- **Dual Coverage**: Both standard and enhanced logging systems work together
- **Performance**: Minimal overhead when debug mode is disabled
- **Backward Compatibility**: Existing logging calls continue to work
- **User Experience**: Real-time debug panel provides immediate feedback

The logging configuration issues in Stage 10 have been completely resolved, providing comprehensive debug visibility that matches the detailed standards established for other GretahAI stages.
