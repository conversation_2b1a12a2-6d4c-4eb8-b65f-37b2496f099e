"""
Stage 10 (V2): Script Playground - Clean Implementation

This is a complete reconstruction of Stage 10 (Script Playground) with clean architecture,
modern state management, and improved user experience. It provides template-based script
generation using optimized scripts from the complete workflow (Stages 1-8).

Key Features:
- Clean state management with proper cleanup
- Modern template loading and selection interface
- Independent API key management
- Streamlined gap analysis workflow
- Template-based AI script generation
- Professional UI with progressive disclosure

This implementation works alongside the legacy stage10.py without modifying existing
functionality, allowing for safe migration and testing.
"""

import logging
import time
from typing import Dict, Any, List, Optional
import streamlit as st

# Import new V2 components
from core.stage10_state_v2 import Stage10StateManager, Stage10WorkflowState
from core.template_storage_v2 import TemplateStorageV2, TemplateMetadata
from core.api_manager_v2 import APIManagerV2, APIKeySource

# Import existing components for integration
from state_manager import StateManager

# Configure logging
logger = logging.getLogger("ScriptWeaver.stages.stage10_v2")


def stage10_script_playground_v2(state: StateManager) -> None:
    """
    Stage 10 (V2): Script Playground - Main entry point.
    
    This function provides a clean, modern implementation of the Script Playground
    with improved architecture and user experience.
    
    Args:
        state: StateManager instance for integration with existing workflow
    """
    try:
        logger.info("Starting Stage 10 (V2) Script Playground")
        
        # Initialize V2 components
        stage10_state = Stage10StateManager.get_state()
        template_storage = TemplateStorageV2()
        api_manager = APIManagerV2()
        
        # Page header
        st.markdown("# 🎮 Script Playground (V2)")
        st.markdown("**Template-based script generation using optimized patterns**")
        
        # Initialize API key
        _initialize_api_key(api_manager)
        
        # Main workflow based on current state
        workflow_state = stage10_state.workflow_state
        
        if workflow_state == Stage10WorkflowState.TEMPLATE_SELECTION:
            _render_template_selection(template_storage)
        
        elif workflow_state == Stage10WorkflowState.GAP_ANALYSIS:
            _render_gap_analysis()
        
        elif workflow_state == Stage10WorkflowState.SCRIPT_GENERATION:
            _render_script_generation()
        
        elif workflow_state == Stage10WorkflowState.SCRIPT_PREVIEW:
            _render_script_preview()
        
        elif workflow_state == Stage10WorkflowState.COMPLETED:
            _render_completion()
        
        # Sidebar navigation
        _render_sidebar_navigation()
        
        # Debug information (if enabled)
        _render_debug_info()
        
    except Exception as e:
        logger.error(f"Error in Stage 10 V2: {e}")
        st.error(f"❌ An error occurred: {str(e)}")


def _initialize_api_key(api_manager: APIManagerV2) -> None:
    """
    Initialize API key with automatic loading and fallback.
    
    Args:
        api_manager: API manager instance
    """
    try:
        # Get API key status
        api_info = api_manager.get_api_key()
        stage10_state = Stage10StateManager.get_state()
        
        if api_info.is_valid:
            # Store in state for later use
            Stage10StateManager.set_api_key(api_info.key, api_info.source.value)
            
            # Show success message in sidebar
            with st.sidebar:
                st.success(f"✅ API Key loaded from {api_info.source.value.replace('_', ' ').title()}")
        
        else:
            # Show API key input in sidebar
            with st.sidebar:
                st.warning("⚠️ Google AI API Key Required")
                
                with st.expander("🔑 API Key Setup", expanded=True):
                    st.markdown("**Automatic Loading Failed**")
                    if api_info.error_message:
                        st.error(api_info.error_message)
                    
                    # Manual input
                    manual_key = st.text_input(
                        "Enter API Key:",
                        type="password",
                        help="Enter your Google AI API key manually",
                        key="stage10_v2_manual_api_key"
                    )
                    
                    if st.button("💾 Save API Key", key="stage10_v2_save_api_key"):
                        if manual_key:
                            manual_info = api_manager.set_manual_api_key(manual_key)
                            if manual_info.is_valid:
                                Stage10StateManager.set_api_key(manual_info.key, manual_info.source.value)
                                st.success("✅ API Key saved successfully!")
                                st.rerun()
                            else:
                                st.error(f"❌ {manual_info.error_message}")
                        else:
                            st.error("❌ Please enter an API key")
    
    except Exception as e:
        logger.error(f"Error initializing API key: {e}")
        st.sidebar.error(f"❌ API Key initialization failed: {str(e)}")


def _render_template_selection(template_storage: TemplateStorageV2) -> None:
    """
    Render the template selection interface.
    
    Args:
        template_storage: Template storage instance
    """
    try:
        st.markdown("## 📋 Step 1: Select Template")
        st.info("💡 Templates are optimized scripts with pre-validated locators from the complete workflow (Stages 1-8)")
        
        # Load available templates
        with st.spinner("🔍 Loading available templates..."):
            templates = template_storage.load_available_templates()
        
        if not templates:
            st.warning("⚠️ No templates available. Please complete the workflow (Stages 1-8) to create templates.")
            return
        
        # Template statistics
        stats = template_storage.get_template_statistics()
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("📊 Total Templates", stats['total_templates'])
        with col2:
            st.metric("⭐ Average Quality", f"{stats['average_quality']:.2f}")
        with col3:
            st.metric("🆕 Recent Templates", stats['recent_templates'])
        
        # Template selection
        with st.expander("🎯 Template Selection", expanded=True):
            # Search and filter
            col1, col2 = st.columns([3, 1])
            
            with col1:
                search_query = st.text_input(
                    "🔍 Search templates:",
                    placeholder="Search by test case ID, objective, or content...",
                    key="stage10_v2_template_search"
                )
            
            with col2:
                script_type_filter = st.selectbox(
                    "📝 Script Type:",
                    options=["All", "optimized", "combined", "step"],
                    key="stage10_v2_script_type_filter"
                )
            
            # Apply search and filters
            filtered_templates = templates
            if search_query:
                filters = {}
                if script_type_filter != "All":
                    filters['script_type'] = script_type_filter
                filtered_templates = template_storage.search_templates(search_query, filters)
            
            # Display templates
            if filtered_templates:
                st.markdown(f"**Found {len(filtered_templates)} templates:**")
                
                for i, template in enumerate(filtered_templates[:10]):  # Show top 10
                    with st.container():
                        col1, col2, col3 = st.columns([3, 2, 1])
                        
                        with col1:
                            st.markdown(f"**{template.template_name}**")
                            st.caption(f"📋 {template.source_test_case_objective[:100]}...")
                        
                        with col2:
                            st.markdown(f"🏷️ {template.script_type.title()}")
                            st.markdown(f"⭐ Quality: {template.quality_score:.2f}")
                        
                        with col3:
                            if st.button(
                                "✅ Select",
                                key=f"stage10_v2_select_template_{i}",
                                use_container_width=True
                            ):
                                # Load full template content
                                template_content = template_storage.get_template_content(template.template_id)
                                if template_content:
                                    Stage10StateManager.set_selected_template(
                                        template.template_id,
                                        template_content['metadata']
                                    )
                                    Stage10StateManager.update_workflow_state(
                                        Stage10WorkflowState.GAP_ANALYSIS,
                                        f"Template selected: {template.template_name}"
                                    )
                                    st.success(f"✅ Selected template: {template.template_name}")
                                    st.rerun()
                                else:
                                    st.error("❌ Failed to load template content")
                        
                        st.divider()
            
            else:
                st.warning("⚠️ No templates match your search criteria")
    
    except Exception as e:
        logger.error(f"Error rendering template selection: {e}")
        st.error(f"❌ Template selection error: {str(e)}")


def _render_gap_analysis() -> None:
    """Render the gap analysis interface."""
    st.markdown("## 🔧 Step 2: Target Test Case Selection")

    stage10_state = Stage10StateManager.get_state()

    # Show selected template info
    if stage10_state.selected_template_id:
        st.success(f"✅ Template selected: {stage10_state.selected_template_id}")

    # Test case selection
    st.markdown("### 🎯 Select Target Test Case")
    st.info("💡 Choose the test case you want to generate a script for using the selected template")

    # Get available test cases from existing state manager
    try:
        from state_manager import StateManager
        main_state = StateManager.get(st)

        if hasattr(main_state, 'test_cases') and main_state.test_cases:
            test_cases = main_state.test_cases

            # Display test cases for selection
            st.markdown(f"**Available Test Cases ({len(test_cases)}):**")

            for i, test_case in enumerate(test_cases):
                test_case_id = test_case.get('Test Case ID', f'TC_{i+1}')
                objective = test_case.get('Test Case Objective', 'No objective specified')

                with st.container():
                    col1, col2 = st.columns([4, 1])

                    with col1:
                        st.markdown(f"**{test_case_id}**")
                        st.caption(f"📋 {objective[:150]}...")

                    with col2:
                        if st.button(
                            "✅ Select",
                            key=f"stage10_v2_select_test_case_{i}",
                            use_container_width=True
                        ):
                            Stage10StateManager.set_selected_test_case(test_case_id, test_case)
                            Stage10StateManager.update_workflow_state(
                                Stage10WorkflowState.SCRIPT_GENERATION,
                                f"Test case selected: {test_case_id}"
                            )
                            st.success(f"✅ Selected test case: {test_case_id}")
                            st.rerun()

                    st.divider()

        else:
            st.warning("⚠️ No test cases available. Please upload and process test cases in the main workflow first.")

            if st.button("📁 Go to Stage 1 (Upload)", key="stage10_v2_go_to_stage1"):
                main_state.advance_to(main_state.current_stage.__class__.STAGE1_UPLOAD, "Navigate to upload from Stage 10 V2")
                st.rerun()

    except Exception as e:
        logger.error(f"Error loading test cases: {e}")
        st.error(f"❌ Error loading test cases: {str(e)}")

        # Fallback - allow manual test case input
        st.markdown("### ✏️ Manual Test Case Input")
        with st.form("stage10_v2_manual_test_case"):
            test_case_id = st.text_input("Test Case ID:", placeholder="e.g., TC_001")
            objective = st.text_area("Test Case Objective:", placeholder="Describe what this test case should accomplish...")

            if st.form_submit_button("✅ Use Manual Test Case"):
                if test_case_id and objective:
                    manual_test_case = {
                        'Test Case ID': test_case_id,
                        'Test Case Objective': objective,
                        'Steps': [],
                        'manual_input': True
                    }
                    Stage10StateManager.set_selected_test_case(test_case_id, manual_test_case)
                    Stage10StateManager.update_workflow_state(
                        Stage10WorkflowState.SCRIPT_GENERATION,
                        f"Manual test case created: {test_case_id}"
                    )
                    st.success(f"✅ Created manual test case: {test_case_id}")
                    st.rerun()
                else:
                    st.error("❌ Please provide both Test Case ID and Objective")


def _render_script_generation() -> None:
    """Render the script generation interface."""
    st.markdown("## ⚡ Step 3: Script Generation")
    st.info("🚧 Script generation implementation coming in Phase 3...")
    
    # Temporary navigation
    if st.button("⏭️ Skip to Preview", key="stage10_v2_skip_generation"):
        Stage10StateManager.update_workflow_state(
            Stage10WorkflowState.SCRIPT_PREVIEW,
            "Script generation skipped"
        )
        st.rerun()


def _render_script_preview() -> None:
    """Render the script preview interface."""
    st.markdown("## 👁️ Step 4: Script Preview")
    st.info("🚧 Script preview implementation coming in Phase 4...")
    
    # Temporary navigation
    if st.button("✅ Complete", key="stage10_v2_complete"):
        Stage10StateManager.update_workflow_state(
            Stage10WorkflowState.COMPLETED,
            "Workflow completed"
        )
        st.rerun()


def _render_completion() -> None:
    """Render the completion interface."""
    st.markdown("## ✅ Workflow Complete")
    st.success("🎉 Script generation completed successfully!")
    
    if st.button("🔄 Start New Generation", key="stage10_v2_restart"):
        Stage10StateManager.clear_state(preserve_api_key=True)
        Stage10StateManager.update_workflow_state(
            Stage10WorkflowState.TEMPLATE_SELECTION,
            "Starting new generation workflow"
        )
        st.rerun()


def _render_sidebar_navigation() -> None:
    """Render sidebar navigation and status."""
    with st.sidebar:
        st.markdown("## 🎮 Script Playground V2")
        
        # Current state indicator
        stage10_state = Stage10StateManager.get_state()
        current_state = stage10_state.workflow_state
        
        st.markdown("### 📍 Current Step")
        
        # Progress indicators
        states = [
            (Stage10WorkflowState.TEMPLATE_SELECTION, "📋 Template Selection"),
            (Stage10WorkflowState.GAP_ANALYSIS, "🔧 Gap Analysis"),
            (Stage10WorkflowState.SCRIPT_GENERATION, "⚡ Script Generation"),
            (Stage10WorkflowState.SCRIPT_PREVIEW, "👁️ Script Preview"),
            (Stage10WorkflowState.COMPLETED, "✅ Completed")
        ]
        
        for state_enum, state_name in states:
            if state_enum == current_state:
                st.markdown(f"**➤ {state_name}**")
            else:
                st.markdown(f"   {state_name}")
        
        st.divider()
        
        # Quick actions
        st.markdown("### ⚡ Quick Actions")
        
        if st.button("🏠 Back to Template Selection", key="stage10_v2_nav_templates"):
            Stage10StateManager.update_workflow_state(
                Stage10WorkflowState.TEMPLATE_SELECTION,
                "Navigation: Back to template selection"
            )
            st.rerun()
        
        if st.button("🧹 Clear All State", key="stage10_v2_clear_state"):
            Stage10StateManager.clear_state(preserve_api_key=True)
            st.success("✅ State cleared!")
            st.rerun()


def _render_debug_info() -> None:
    """Render debug information if enabled."""
    import os
    
    if os.getenv("STAGE10_V2_DEBUG", "false").lower() == "true":
        with st.expander("🐛 Debug Information", expanded=False):
            stage10_state = Stage10StateManager.get_state()
            
            st.markdown("**Stage 10 V2 State:**")
            st.json({
                'workflow_state': stage10_state.workflow_state.value,
                'session_id': stage10_state.session_id,
                'selected_template_id': stage10_state.selected_template_id,
                'selected_test_case_id': stage10_state.selected_test_case_id,
                'api_key_source': stage10_state.api_key_source,
                'api_key_validated': stage10_state.api_key_validated,
                'last_activity': time.ctime(stage10_state.last_activity)
            })
            
            st.markdown("**Session State Keys:**")
            relevant_keys = [k for k in st.session_state.keys() if 'stage10_v2' in k]
            st.json(relevant_keys)
