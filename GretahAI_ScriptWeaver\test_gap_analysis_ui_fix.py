"""
Test script to verify the gap analysis UI fix for interactive selector button interference.

This script tests the new render_interactive_selector_section function and ensures
that interactive selector buttons work correctly without causing UI interference.
"""

import sys
import os
import streamlit as st

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interactive_selector_removal():
    """Test the removal of interactive selector functionality from Stage 10."""
    st.title("🔧 Interactive Selector Removal Test")
    st.markdown("Testing the removal of interactive selector functionality from Stage 10 gap analysis.")
    
    # Test gaps with locator types
    test_gaps = [
        {
            'id': 'username_locator',
            'type': 'locator',
            'description': 'Username input field selector',
            'required': True,
            'suggested_values': ['#username', '.username-input', '[name="username"]']
        },
        {
            'id': 'password_locator',
            'type': 'locator',
            'description': 'Password input field selector',
            'required': True,
            'suggested_values': ['#password', '.password-input', '[name="password"]']
        },
        {
            'id': 'submit_locator',
            'type': 'locator',
            'description': 'Submit button selector',
            'required': True,
            'suggested_values': ['#submit', '.submit-btn', 'button[type="submit"]']
        },
        {
            'id': 'test_data_gap',
            'type': 'test_data',
            'description': 'Test username value',
            'required': True,
            'suggested_values': ['testuser', 'admin', '<EMAIL>']
        }
    ]
    
    # Configuration section
    st.header("Configuration")
    website_url = st.text_input(
        "Website URL for Interactive Selection",
        value="https://example.com",
        placeholder="https://your-target-website.com"
    )
    
    st.markdown("---")
    
    # Test the removal of interactive selector section
    st.header("Test 1: Interactive Selector Removal Verification")

    try:
        # Verify that the interactive selector function has been removed
        from core.gap_analysis import render_interactive_selector_section
        st.error("❌ Interactive selector function still exists - should have been removed!")
    except ImportError:
        st.success("✅ Interactive selector function successfully removed from gap analysis module")
    except Exception as e:
        st.warning(f"⚠️ Unexpected error when checking for removed function: {e}")
    
    st.markdown("---")
    
    # Test the simplified gap filling form
    st.header("Test 2: Simplified Gap Filling Form")

    try:
        from core.gap_analysis import display_gap_filling_form

        # Display the gap filling form (no website URL needed for templates)
        form_key = "test_gap_form"
        result = display_gap_filling_form(test_gaps, form_key, None)

        if result is not None:
            st.success("✅ Gap filling form submitted successfully!")
            st.json(result)
        else:
            st.info("⏳ Gap filling form displayed, waiting for submission")
            st.info("💡 Note: Locator gaps now focus on NEW elements only, since templates have pre-validated locators")

    except Exception as e:
        st.error(f"❌ Error testing gap filling form: {e}")
        st.exception(e)
    
    st.markdown("---")

    # Test Stage 10 interactive selector removal
    st.header("Test 3: Stage 10 Interactive Selector Removal Verification")

    # Test that the Stage 10 interactive selector function has been removed
    try:
        from stages.stage10 import _render_stage10_interactive_selector_section
        st.error("❌ Stage 10 interactive selector function still exists - should have been removed!")
    except ImportError:
        st.success("✅ Stage 10 interactive selector function successfully removed")
    except Exception as e:
        st.warning(f"⚠️ Unexpected error when checking for removed Stage 10 function: {e}")

    # Test that Stage 10 now focuses on templates
    st.info("💡 Stage 10 now focuses on template-based script generation with pre-validated locators")
    st.markdown("**Template Benefits:**")
    st.markdown("- ✅ Pre-validated locators from interactive selection")
    st.markdown("- ✅ Tested and optimized automation patterns")
    st.markdown("- ✅ Best practices and error handling")
    st.markdown("- ✅ Quality preservation from original workflow")

    st.markdown("---")

    # Test session state inspection
    st.header("Test 4: Session State Cleanup Verification")

    # Check for any remaining interactive locator keys (should be none)
    interactive_keys = [key for key in st.session_state.keys() if "interactive_locator_" in key]

    if interactive_keys:
        st.warning(f"⚠️ Found {len(interactive_keys)} interactive locator keys in session state - these should be cleaned up")
        with st.expander("Remaining Interactive Locator Keys", expanded=False):
            for key in interactive_keys:
                value = st.session_state.get(key, "")
                st.write(f"**{key}**: `{value}`")
    else:
        st.success("✅ No interactive locator keys found in session state - cleanup successful")
    
    # Show all session state keys for debugging
    with st.expander("All Session State Keys", expanded=False):
        st.write(f"Total keys: {len(st.session_state.keys())}")
        for key in sorted(st.session_state.keys()):
            if not key.startswith('FormSubmitter:'):  # Skip internal Streamlit keys
                value = st.session_state.get(key)
                if isinstance(value, str) and len(value) > 100:
                    value = value[:100] + "..."
                st.write(f"**{key}**: {value}")

if __name__ == "__main__":
    test_interactive_selector_removal()
