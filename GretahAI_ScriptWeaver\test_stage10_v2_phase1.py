"""
Test Script for Stage 10 V2 Phase 1 Implementation

This script validates the Phase 1 implementation of Stage 10 V2, including:
- State management functionality
- Template storage interface
- API key management
- Integration with existing systems

Run this script to verify that Phase 1 components are working correctly
before proceeding to Phase 2 implementation.
"""

import os
import sys
import logging
import tempfile
import json
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_stage10_v2_phase1")


def test_state_management():
    """Test Stage 10 V2 state management functionality."""
    print("\n🧪 Testing State Management...")
    
    try:
        from core.stage10_state_v2 import Stage10StateManager, Stage10WorkflowState, Stage10SessionState
        
        # Test state initialization
        print("  ✓ Importing state management modules")
        
        # Test workflow state enum
        assert Stage10WorkflowState.TEMPLATE_SELECTION.value == "template_selection"
        assert Stage10WorkflowState.GAP_ANALYSIS.value == "gap_analysis"
        print("  ✓ Workflow state enum working correctly")
        
        # Test session state dataclass
        state = Stage10SessionState()
        assert state.workflow_state == Stage10WorkflowState.TEMPLATE_SELECTION
        assert state.selected_template_id is None
        assert isinstance(state.temporary_session_keys, set)
        print("  ✓ Session state dataclass working correctly")
        
        print("✅ State Management tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ State Management test failed: {e}")
        return False


def test_template_storage():
    """Test template storage interface functionality."""
    print("\n🧪 Testing Template Storage...")
    
    try:
        from core.template_storage_v2 import TemplateStorageV2, TemplateMetadata
        
        # Test template storage initialization
        storage = TemplateStorageV2()
        print("  ✓ Template storage initialization")
        
        # Test template metadata dataclass
        metadata = TemplateMetadata(
            template_id="test_template",
            original_script_id="test_script",
            template_name="Test Template",
            source_test_case_id="TC_001",
            source_test_case_objective="Test objective",
            creation_timestamp=1234567890.0,
            script_type="optimized",
            line_count=50,
            file_size=1024,
            locators_validated=True,
            optimization_level="optimized"
        )
        assert metadata.template_id == "test_template"
        assert metadata.locators_validated is True
        print("  ✓ Template metadata dataclass working correctly")
        
        # Test statistics method (should not fail even with no data)
        stats = storage.get_template_statistics()
        assert isinstance(stats, dict)
        assert 'total_templates' in stats
        print("  ✓ Template statistics method working")
        
        print("✅ Template Storage tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Template Storage test failed: {e}")
        return False


def test_api_management():
    """Test API key management functionality."""
    print("\n🧪 Testing API Management...")
    
    try:
        from core.api_manager_v2 import APIManagerV2, APIKeySource, APIKeyInfo
        
        # Test API manager initialization
        api_manager = APIManagerV2()
        print("  ✓ API manager initialization")
        
        # Test API key info dataclass
        api_info = APIKeyInfo(
            key="test_key",
            source=APIKeySource.MANUAL_INPUT,
            is_valid=True
        )
        assert api_info.key == "test_key"
        assert api_info.source == APIKeySource.MANUAL_INPUT
        print("  ✓ API key info dataclass working correctly")
        
        # Test API key validation
        assert api_manager._validate_api_key("AIzaSyDummyKeyForTesting123456789") is True
        assert api_manager._validate_api_key("short") is False
        assert api_manager._validate_api_key("") is False
        print("  ✓ API key validation working correctly")
        
        # Test API key status (should not fail even without valid keys)
        status = api_manager.get_api_key_status()
        assert isinstance(status, dict)
        assert 'has_api_key' in status
        print("  ✓ API key status method working")
        
        print("✅ API Management tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ API Management test failed: {e}")
        return False


def test_integration():
    """Test integration functionality."""
    print("\n🧪 Testing Integration...")
    
    try:
        from core.stage10_integration_v2 import Stage10IntegrationV2, Stage10Implementation
        
        # Test integration manager initialization
        integration = Stage10IntegrationV2()
        print("  ✓ Integration manager initialization")
        
        # Test implementation enum
        assert Stage10Implementation.LEGACY.value == "legacy"
        assert Stage10Implementation.V2.value == "v2"
        print("  ✓ Implementation enum working correctly")
        
        # Test implementation detection (should default to legacy for safety)
        active_impl = integration.get_active_implementation()
        assert isinstance(active_impl, Stage10Implementation)
        print(f"  ✓ Active implementation: {active_impl.value}")
        
        # Test compatibility check
        is_compatible = integration._check_v2_compatibility()
        print(f"  ✓ V2 compatibility check: {is_compatible}")
        
        print("✅ Integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def test_config_file_creation():
    """Test creation of a sample config file for API key testing."""
    print("\n🧪 Testing Config File Creation...")
    
    try:
        # Create a sample config file for testing
        config_path = "test_config.json"
        test_config = {
            "google_ai_api_key": "AIzaSyDummyKeyForTesting123456789",
            "test_mode": True
        }
        
        with open(config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        print(f"  ✓ Created test config file: {config_path}")
        
        # Test loading from the config file
        from core.api_manager_v2 import APIManagerV2
        
        # Temporarily modify the config paths for testing
        original_paths = APIManagerV2.CONFIG_PATHS
        APIManagerV2.CONFIG_PATHS = [config_path] + original_paths
        
        api_manager = APIManagerV2()
        config_info = api_manager._load_from_config()
        
        # Restore original paths
        APIManagerV2.CONFIG_PATHS = original_paths
        
        assert config_info.is_valid is True
        assert config_info.key == "AIzaSyDummyKeyForTesting123456789"
        print("  ✓ Config file loading working correctly")
        
        # Clean up
        os.remove(config_path)
        print("  ✓ Cleaned up test config file")
        
        print("✅ Config File tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Config File test failed: {e}")
        # Clean up on failure
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
        return False


def test_environment_variables():
    """Test environment variable handling."""
    print("\n🧪 Testing Environment Variables...")
    
    try:
        from core.api_manager_v2 import APIManagerV2
        
        # Test with environment variable
        test_key = "AIzaSyTestEnvironmentKey123456789"
        os.environ["GOOGLE_AI_API_KEY"] = test_key
        
        api_manager = APIManagerV2()
        env_info = api_manager._load_from_environment()
        
        assert env_info.is_valid is True
        assert env_info.key == test_key
        print("  ✓ Environment variable loading working correctly")
        
        # Clean up
        del os.environ["GOOGLE_AI_API_KEY"]
        print("  ✓ Cleaned up test environment variable")
        
        print("✅ Environment Variable tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Environment Variable test failed: {e}")
        # Clean up on failure
        if "GOOGLE_AI_API_KEY" in os.environ:
            del os.environ["GOOGLE_AI_API_KEY"]
        return False


def run_all_tests():
    """Run all Phase 1 tests."""
    print("🚀 Starting Stage 10 V2 Phase 1 Tests")
    print("=" * 50)
    
    tests = [
        ("State Management", test_state_management),
        ("Template Storage", test_template_storage),
        ("API Management", test_api_management),
        ("Integration", test_integration),
        ("Config File Creation", test_config_file_creation),
        ("Environment Variables", test_environment_variables)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All Phase 1 tests passed! Ready for Phase 2 implementation.")
        return True
    else:
        print("⚠️ Some tests failed. Please fix issues before proceeding to Phase 2.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
